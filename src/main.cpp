#include <iostream>
#include <string>
#include <cstring>
#include <map>
#if defined(_WIN32)
#include <windows.h>
#endif
#include <GLFW/glfw3.h>
#include <GL/gl.h>
#include <imgui.h>
#include <imgui_impl_glfw.h>
#include <imgui_impl_opengl3.h>
#include "imfilebrowser.h"
#include "piano_keyboard.h"
#include "opengl_renderer.h"
#include "audio_engine.h"
#include "config_manager.h"

// Global variables for mouse input
double g_mouse_x = 0.0;
double g_mouse_y = 0.0;
bool g_mouse_is_down = false;

// Global variables for window management
PianoKeyboard* g_piano = nullptr;
OpenGLRenderer* g_renderer = nullptr;
AudioEngine* g_audio_engine = nullptr;

// Global variables for audio
AudioEngine g_audio;
bool g_audio_initialized = false;

// Global configuration manager
ConfigManager g_config;

// Global file browser for soundfont selection
ImGui::FileBrowser g_soundfont_browser(ImGuiFileBrowserFlags_CloseOnEsc);

// Global variables for PC keyboard input
static std::map<int, int> g_key_to_note_map; // Maps GLFW key codes to MIDI note numbers
static std::map<int, bool> g_key_pressed_state; // Tracks which keys are currently pressed

// Error callback for GLFW
void glfw_error_callback(int error, const char* description) {
    std::cerr << "GLFW Error " << error << ": " << description << std::endl;
}

// Mouse callback for GLFW
void mouse_button_callback(GLFWwindow* window, int button, int action, int mods) {
    // Let ImGui handle the event first
    ImGuiIO& io = ImGui::GetIO();

    if (button == GLFW_MOUSE_BUTTON_LEFT) {
        if (action == GLFW_PRESS) {
            // Only set mouse down if ImGui is not using the mouse
            g_mouse_is_down = !io.WantCaptureMouse;
        } else if (action == GLFW_RELEASE) {
            g_mouse_is_down = false;
        }
    }
}

void cursor_position_callback(GLFWwindow* window, double xpos, double ypos) {
    g_mouse_x = xpos;
    g_mouse_y = ypos;
}

void window_size_callback(GLFWwindow* window, int width, int height) {
    if (g_piano && g_renderer) {
        g_renderer->SetViewport(width, height);
        g_piano->UpdateLayout(width, height);

        // Save window size to config
        auto& config = g_config.GetConfig();
        config.window.width = width;
        config.window.height = height;
        g_config.AutoSave();
    }
}

// Keyboard callback for PC keyboard input
void key_callback(GLFWwindow* window, int key, int scancode, int action, int mods) {
    // Handle special key combinations for panic functions
    if (action == GLFW_PRESS) {
        // Ctrl+Shift+P = Panic Restart
        if (key == GLFW_KEY_P && (mods & GLFW_MOD_CONTROL) && (mods & GLFW_MOD_SHIFT)) {
            std::cout << "Panic restart triggered by keyboard shortcut (Ctrl+Shift+P)" << std::endl;
            if (g_audio_engine) {
                g_audio_engine->PanicRestart();
            }
            return;
        }

        // Ctrl+Shift+S = Emergency Stop All Notes
        if (key == GLFW_KEY_S && (mods & GLFW_MOD_CONTROL) && (mods & GLFW_MOD_SHIFT)) {
            std::cout << "Emergency stop triggered by keyboard shortcut (Ctrl+Shift+S)" << std::endl;
            if (g_audio_engine) {
                g_audio_engine->EmergencyStopAllNotes();
            }
            return;
        }

        // Ctrl+Shift+T = Test Audio
        if (key == GLFW_KEY_T && (mods & GLFW_MOD_CONTROL) && (mods & GLFW_MOD_SHIFT)) {
            std::cout << "Test audio triggered by keyboard shortcut (Ctrl+Shift+T)" << std::endl;
            if (g_audio_engine) {
                g_audio_engine->PlayTestTone();
            }
            return;
        }
    }

    // Check if ImGui wants to capture keyboard input
    ImGuiIO& io = ImGui::GetIO();
    if (io.WantCaptureKeyboard) {
        return; // Let ImGui handle the keyboard input
    }

    // Check if this key is mapped to a note
    auto it = g_key_to_note_map.find(key);
    if (it != g_key_to_note_map.end()) {
        int note = it->second;

        if (action == GLFW_PRESS) {
            // Key pressed - only trigger if not already pressed (to avoid repeats)
            if (g_key_pressed_state[key] == false) {
                g_key_pressed_state[key] = true;
                if (g_piano) {
                    g_piano->SetKeyPressed(note, true);
                }
            }
        } else if (action == GLFW_RELEASE) {
            // Key released
            g_key_pressed_state[key] = false;
            if (g_piano) {
                g_piano->SetKeyPressed(note, false);
            }
        }
    }
}

// Initialize PC keyboard to MIDI note mapping
void initialize_keyboard_mapping() {
    // Clear existing mappings
    g_key_to_note_map.clear();
    g_key_pressed_state.clear();

    // Helper function to create note mapping
    auto map_key = [](int glfw_key, int note) {
        g_key_to_note_map[glfw_key] = note;
        g_key_pressed_state[glfw_key] = false;
    };

    // Lower row - Piano layout starting from A3 (Z = A3/ラ)
    map_key(GLFW_KEY_A, 56);      // 
    map_key(GLFW_KEY_Z, 57);      // A3 - ラ
    map_key(GLFW_KEY_S, 58);      // A#3/Bb3 (black key)
    map_key(GLFW_KEY_X, 59);      // B3 - シ
    map_key(GLFW_KEY_C, 60);      // C4 - ド (Middle C)
    map_key(GLFW_KEY_F, 61);      // C#4/Db4 (black key)
    map_key(GLFW_KEY_V, 62);      // D4 - レ
    map_key(GLFW_KEY_G, 63);      // D#4/Eb4 (black key)
    map_key(GLFW_KEY_B, 64);      // E4 - ミ
    map_key(GLFW_KEY_N, 65);      // F4 - ファ
    map_key(GLFW_KEY_J, 66);      // F#4/Gb4 (black key)
    map_key(GLFW_KEY_M, 67);      // G4 - ソ
    map_key(GLFW_KEY_K, 68);      // G#4/Ab4 (black key)
    map_key(GLFW_KEY_COMMA, 69);  // A4 - ラ
    map_key(GLFW_KEY_L, 70);      // A#4/Bb4 (black key)
    map_key(GLFW_KEY_PERIOD, 71); // B4 - シ
    map_key(GLFW_KEY_SEMICOLON, 72); // C5 - ド
    map_key(GLFW_KEY_SLASH, 73);  // C#5/Db5 (black key)

    // Upper row - Higher octave starting from A4 (Q = A4/ラ)
    map_key(GLFW_KEY_1, 68);      // 
    map_key(GLFW_KEY_Q, 69);      // A4 - ラ
    map_key(GLFW_KEY_2, 70);      // A#4/Bb4 (black key)
    map_key(GLFW_KEY_W, 71);      // B4 - シ
    map_key(GLFW_KEY_E, 72);      // C5 - ド
    map_key(GLFW_KEY_4, 73);      // C#5/Db5 (black key)
    map_key(GLFW_KEY_R, 74);      // D5 - レ
    map_key(GLFW_KEY_5, 75);      // D#5/Eb5 (black key)
    map_key(GLFW_KEY_T, 76);      // E5 - ミ
    map_key(GLFW_KEY_Y, 77);      // F5 - ファ
    map_key(GLFW_KEY_7, 78);      // F#5/Gb5 (black key)
    map_key(GLFW_KEY_U, 79);      // G5 - ソ
    map_key(GLFW_KEY_8, 80);      // G#5/Ab5 (black key)
    map_key(GLFW_KEY_I, 81);      // A5 - ラ
    map_key(GLFW_KEY_9, 82);      // A#5/Bb5 (black key)
    map_key(GLFW_KEY_O, 83);      // B5 - シ
    map_key(GLFW_KEY_P, 84);      // C6 - ド
    map_key(GLFW_KEY_MINUS, 85);  // C#6/Db6 (black key)
    map_key(GLFW_KEY_LEFT_BRACKET, 86);  // D6 - レ
    map_key(GLFW_KEY_EQUAL, 87);  // D#6/Eb6 (black key)
    map_key(GLFW_KEY_RIGHT_BRACKET, 88); // E6 - ミ
}

int main(int argc, char** argv) {
    std::cout << "Starting Piano Keyboard Application..." << std::endl;
    std::cout.flush();

    // Initialize configuration manager
    if (!g_config.Initialize()) {
        std::cerr << "Failed to initialize configuration manager" << std::endl;
        return -1;
    }

    // Setup GLFW
    glfwSetErrorCallback(glfw_error_callback);
    if (!glfwInit()) {
        std::cerr << "Failed to initialize GLFW" << std::endl;
        return -1;
    }

    // GL 3.0 + GLSL 130
    const char* glsl_version = "#version 130";
    glfwWindowHint(GLFW_CONTEXT_VERSION_MAJOR, 3);
    glfwWindowHint(GLFW_CONTEXT_VERSION_MINOR, 0);

    // Create window with graphics context (using config settings)
    const auto& window_config = g_config.GetConfig().window;
    GLFWwindow* window = glfwCreateWindow(window_config.width, window_config.height, "Piano Keyboard", NULL, NULL);
    if (window == NULL) {
        std::cerr << "Failed to create GLFW window" << std::endl;
        glfwTerminate();
        return -1;
    }
    glfwMakeContextCurrent(window);
    glfwSwapInterval(1); // Enable vsync

    // Set GLFW callbacks
    glfwSetMouseButtonCallback(window, mouse_button_callback);
    glfwSetCursorPosCallback(window, cursor_position_callback);
    glfwSetWindowSizeCallback(window, window_size_callback);
    glfwSetKeyCallback(window, key_callback);

    // Initialize PC keyboard to MIDI note mapping
    initialize_keyboard_mapping();

    // Setup Dear ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;

    // Setup Dear ImGui style
    ImGui::StyleColorsDark();

    // Setup Platform/Renderer backends
    ImGui_ImplGlfw_InitForOpenGL(window, true);
    ImGui_ImplOpenGL3_Init(glsl_version);

    // Initialize OpenGL renderer
    OpenGLRenderer renderer;
    renderer.Initialize(1280, 720);

    // Initialize audio engine
    std::cout << "Initializing Audio Engine..." << std::endl;
    std::cout.flush();
    g_audio_initialized = g_audio.Initialize();
    if (!g_audio_initialized) {
        std::cerr << "Warning: Audio engine failed to initialize. Audio will be disabled." << std::endl;
        std::cerr.flush();
    } else {
        std::cout << "Audio engine initialized successfully!" << std::endl;
        std::cout.flush();
    }

    // Initialize piano keyboard
    PianoKeyboard piano;
    piano.Initialize(&g_audio);

    // Set global pointers for callbacks
    g_piano = &piano;
    g_renderer = &renderer;
    g_audio_engine = &g_audio;

    // Register piano keyboard with audio engine for MIDI visual feedback
    g_audio.SetPianoKeyboard(&piano);

    // Get configuration reference
    auto& config = g_config.GetConfig();

    // Initialize piano keyboard with config settings
    piano.SetAutoLayout(config.keyboard.auto_layout);
    piano.SetKeyboardMargin(config.keyboard.keyboard_margin);
    piano.SetWhiteKeySize(Vec2(config.keyboard.white_key_width, config.keyboard.white_key_height));
    piano.SetBlackKeySize(Vec2(config.keyboard.black_key_width, config.keyboard.black_key_height));
    piano.SetAudioEnabled(config.audio.audio_enabled);

    // Set audio settings from config
    g_audio.SetVolume(config.audio.volume);
    g_audio.SetMaxPolyphony(config.audio.polyphony);
    if (!config.audio.soundfont_path.empty() && config.audio.soundfont_path != "default.sf2") {
        g_audio.LoadSoundfont(config.audio.soundfont_path);
    }

    // Set audio limiter settings from config
    if (g_audio.IsInitialized()) {
        g_audio.SetLimiterEnabled(config.audio.limiter_enabled);
        AudioLimiter* limiter = g_audio.GetAudioLimiter();
        if (limiter) {
            limiter->SetThreshold(config.audio.limiter_threshold);
            limiter->SetReleaseTime(config.audio.limiter_release_time);
            limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
        }
    }

    // Initialize file browser for soundfont selection
    g_soundfont_browser.SetTitle("Select Soundfont File");
    g_soundfont_browser.SetTypeFilters({".sf2", ".SF2", ".sfz", ".SFZ"});
    g_soundfont_browser.SetWindowSize(800, 600);

    // Main loop
    while (!glfwWindowShouldClose(window)) {
        glfwPollEvents();

        // Start the Dear ImGui frame
        ImGui_ImplOpenGL3_NewFrame();
        ImGui_ImplGlfw_NewFrame();
        ImGui::NewFrame();

        // Main menu bar
        if (ImGui::BeginMainMenuBar()) {
            if (ImGui::BeginMenu("View")) {
                ImGui::MenuItem("Settings", NULL, &config.display.show_settings);
                ImGui::MenuItem("Debug Info", NULL, &config.display.show_debug);
                ImGui::MenuItem("Audio Limiter", NULL, &config.display.show_audio_limiter);
                ImGui::EndMenu();
            }
            if (ImGui::BeginMenu("Piano")) {
                if (ImGui::MenuItem("Reset All Keys")) {
                    for (int i = 0; i < 128; ++i) {
                        piano.SetKeyPressed(i, false);
                    }
                }
                ImGui::EndMenu();
            }
            ImGui::EndMainMenuBar();
        }

        // Update piano keyboard
        piano.Update();

        // Audio health monitoring (check every 60 frames, about once per second at 60fps)
        static int audio_check_counter = 0;
        static bool last_audio_status = true;
        audio_check_counter++;
        if (audio_check_counter >= 60) {
            audio_check_counter = 0;
            if (g_audio.IsInitialized()) {
                bool current_audio_status = g_audio.IsAudioWorking();
                if (last_audio_status && !current_audio_status) {
                    // Audio just stopped working
                    std::cout << "WARNING: Audio problem detected! Consider using panic restart." << std::endl;
                }
                last_audio_status = current_audio_status;
            }
        }

        // Process audio for all engines
        g_audio.ProcessAudio();

        // Handle mouse input for piano keyboard (only if ImGui is not using the mouse)
        ImGuiIO& io = ImGui::GetIO();
        bool should_handle_input = !io.WantCaptureMouse && g_mouse_is_down;
        piano.HandleInput(g_mouse_x, g_mouse_y, should_handle_input);

        // Get window size for debug info
        int display_w, display_h;
        glfwGetFramebufferSize(window, &display_w, &display_h);

        // Update renderer viewport
        renderer.SetViewport(display_w, display_h);

        // Update piano keyboard layout based on window size
        piano.UpdateLayout(display_w, display_h);

        // Settings window
        if (config.display.show_settings) {
            ImGui::Begin("Settings", &config.display.show_settings);

            ImGui::Text("Keyboard Settings");
            ImGui::Separator();

            if (ImGui::Checkbox("Auto Layout", &config.keyboard.auto_layout)) {
                piano.SetAutoLayout(config.keyboard.auto_layout);
                g_config.AutoSave();
            }

            if (config.keyboard.auto_layout) {
                if (ImGui::SliderFloat("Keyboard Margin", &config.keyboard.keyboard_margin, 10.0f, 200.0f)) {
                    piano.SetKeyboardMargin(config.keyboard.keyboard_margin);
                    g_config.AutoSave();
                }
            } else {
                if (ImGui::SliderFloat2("White Key Size", &config.keyboard.white_key_width, 10.0f, 50.0f)) {
                    piano.SetWhiteKeySize(Vec2(config.keyboard.white_key_width, config.keyboard.white_key_height));
                    g_config.AutoSave();
                }

                if (ImGui::SliderFloat2("Black Key Size", &config.keyboard.black_key_width, 5.0f, 30.0f)) {
                    piano.SetBlackKeySize(Vec2(config.keyboard.black_key_width, config.keyboard.black_key_height));
                    g_config.AutoSave();
                }
            }

            ImGui::Text("PC Keyboard Input");
            ImGui::Separator();
            ImGui::TextWrapped("You can play notes using your PC keyboard:");
            ImGui::Text("Lower row: Z(A3) X(B3) C(C4) V(D4) B(E4) N(F4) M(G4) ,(A4)");
            ImGui::Text("Black keys: S F G J K L");
            ImGui::Text("Upper row: Q(A4) W(B4) E(C5) R(D5) T(E5) Y(F5) U(G5) I(A5)");
            ImGui::Text("Black keys: 2 4 5 7 8 9 - =");
            ImGui::TextWrapped("Z key = A3 (ラ), C key = Middle C (C4). Piano layout with white and black keys.");

            ImGui::Spacing();
            ImGui::Text("Audio Panic Shortcuts");
            ImGui::Separator();
            ImGui::TextColored(ImVec4(1.0f, 0.8f, 0.0f, 1.0f), "Ctrl+Shift+P: PANIC RESTART (emergency audio restart)");
            ImGui::TextColored(ImVec4(0.8f, 1.0f, 0.0f, 1.0f), "Ctrl+Shift+S: Emergency Stop All Notes");
            ImGui::TextColored(ImVec4(0.0f, 0.8f, 1.0f, 1.0f), "Ctrl+Shift+T: Test Audio (play Middle C)");
            ImGui::Spacing();

            ImGui::Text("Audio Settings");
            ImGui::Separator();

            if (g_audio_initialized) {
                if (ImGui::Checkbox("Enable Audio", &config.audio.audio_enabled)) {
                    piano.SetAudioEnabled(config.audio.audio_enabled);
                    g_config.AutoSave();
                }

                if (ImGui::SliderFloat("Volume", &config.audio.volume, 0.0f, 1.0f)) {
                    g_audio.SetVolume(config.audio.volume);
                    g_config.AutoSave();
                }

                ImGui::Text("Soundfont Settings:");
                ImGui::Separator();

                // Show current soundfont status
                if (g_audio.IsSoundfontLoaded()) {
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Soundfont loaded:");
                    ImGui::Text("  %s", g_audio.GetCurrentSoundfontPath().c_str());
                } else {
                    ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "No soundfont loaded");
                    ImGui::Text("Using default MIDI sounds");
                }

                ImGui::Spacing();

                // Soundfont file input
                ImGui::Text("Load Soundfont File (.sf2, .sfz):");

                // Use config soundfont path as buffer (update every frame to reflect changes)
                static char soundfont_buffer[512];
                static std::string last_soundfont_path;

                // Update buffer if config path has changed (e.g., from file browser)
                if (last_soundfont_path != config.audio.soundfont_path) {
                    strncpy(soundfont_buffer, config.audio.soundfont_path.c_str(), sizeof(soundfont_buffer) - 1);
                    soundfont_buffer[sizeof(soundfont_buffer) - 1] = '\0';
                    last_soundfont_path = config.audio.soundfont_path;
                }

                ImGui::PushItemWidth(-120);
                if (ImGui::InputText("##soundfont", soundfont_buffer, sizeof(soundfont_buffer))) {
                    config.audio.soundfont_path = std::string(soundfont_buffer);
                    last_soundfont_path = config.audio.soundfont_path;
                    g_config.AutoSave();
                }
                ImGui::PopItemWidth();

                ImGui::SameLine();
                if (ImGui::Button("Browse...")) {
                    g_soundfont_browser.Open();
                }

                if (ImGui::Button("Load Soundfont")) {
                    if (g_audio.LoadSoundfont(config.audio.soundfont_path)) {
                        std::cout << "Soundfont loaded successfully: " << config.audio.soundfont_path << std::endl;
                        g_config.AutoSave();
                    } else {
                        std::cerr << "Failed to load soundfont: " << config.audio.soundfont_path << std::endl;
                    }
                }

                ImGui::SameLine();
                if (ImGui::Button("Clear Soundfont")) {
                    // Clear the current soundfont to use default MIDI sounds
                    if (g_audio.LoadSoundfont("__clear_soundfont__")) {
                        config.audio.soundfont_path = "default.sf2";
                        std::cout << "Soundfont cleared, using default MIDI sounds" << std::endl;
                        g_config.AutoSave();
                    }
                }

                ImGui::Spacing();
                ImGui::Separator();
                ImGui::Text("Audio Test:");

                if (ImGui::Button("Play Test Tone")) {
                    g_audio.PlayTestTone();
                }

                ImGui::SameLine();
                if (ImGui::Button("Show Devices")) {
                    g_audio.PrintDeviceInfo();
                }




                ImGui::Spacing();
                ImGui::TextWrapped("Note: Place .sf2 or .sfz files in the executable directory or use full paths. Popular soundfonts include FluidR3_GM.sf2, GeneralUser_GS.sf2, or Timbres_Of_Heaven.sf2");

                // BASS FX Settings
                ImGui::Spacing();
                ImGui::Text("BASS FX Audio Effects:");
                ImGui::Separator();

                if (ImGui::Checkbox("Enable BASS FX", &config.audio.bassfx_enabled)) {
                    g_audio.SetBASS_FXEnabled(config.audio.bassfx_enabled);
                    g_config.AutoSave();
                }

                if (config.audio.bassfx_enabled) {
                    ImGui::Indent();

                    // Reverb Effect
                    if (ImGui::Checkbox("Reverb", &config.audio.bassfx_reverb_enabled)) {
                        g_audio.SetReverbEnabled(config.audio.bassfx_reverb_enabled);
                        g_config.AutoSave();
                    }
                    if (config.audio.bassfx_reverb_enabled) {
                        ImGui::SameLine();
                        if (ImGui::SliderFloat("##reverb_mix", &config.audio.bassfx_reverb_mix, 0.0f, 1.0f, "Mix: %.2f")) {
                            g_audio.SetReverbMix(config.audio.bassfx_reverb_mix);
                            g_config.AutoSave();
                        }
                    }

                    // Chorus Effect
                    if (ImGui::Checkbox("Chorus", &config.audio.bassfx_chorus_enabled)) {
                        g_audio.SetChorusEnabled(config.audio.bassfx_chorus_enabled);
                        g_config.AutoSave();
                    }
                    if (config.audio.bassfx_chorus_enabled) {
                        ImGui::SameLine();
                        if (ImGui::SliderFloat("##chorus_mix", &config.audio.bassfx_chorus_mix, 0.0f, 1.0f, "Mix: %.2f")) {
                            g_audio.SetChorusMix(config.audio.bassfx_chorus_mix);
                            g_config.AutoSave();
                        }
                    }

                    // Echo Effect
                    if (ImGui::Checkbox("Echo", &config.audio.bassfx_echo_enabled)) {
                        g_audio.SetEchoEnabled(config.audio.bassfx_echo_enabled);
                        g_config.AutoSave();
                    }
                    if (config.audio.bassfx_echo_enabled) {
                        ImGui::SameLine();
                        if (ImGui::SliderFloat("##echo_mix", &config.audio.bassfx_echo_mix, 0.0f, 1.0f, "Mix: %.2f")) {
                            g_audio.SetEchoMix(config.audio.bassfx_echo_mix);
                            g_config.AutoSave();
                        }
                    }

                    // Compressor Effect
                    if (ImGui::Checkbox("Compressor", &config.audio.bassfx_compressor_enabled)) {
                        g_audio.SetCompressorEnabled(config.audio.bassfx_compressor_enabled);
                        g_config.AutoSave();
                    }
                    if (config.audio.bassfx_compressor_enabled) {
                        ImGui::SameLine();
                        if (ImGui::SliderFloat("##compressor_ratio", &config.audio.bassfx_compressor_ratio, 1.0f, 20.0f, "Ratio: %.1f:1")) {
                            g_audio.SetCompressorRatio(config.audio.bassfx_compressor_ratio);
                            g_config.AutoSave();
                        }
                    }

                    ImGui::Unindent();

                    ImGui::Spacing();
                    ImGui::TextWrapped("Note: BASS FX effects are applied to the MIDI audio output. Enable individual effects and adjust their parameters for different sound characteristics.");
                } else {
                    ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Enable BASS FX to access audio effects");
                }

            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Audio engine not available");
                ImGui::Text("BASS libraries may not be installed");
            }

            ImGui::Text("Display Settings");
            ImGui::Separator();

            if (ImGui::ColorEdit3("Background Color", config.display.background_color)) {
                g_config.AutoSave();
            }

            ImGui::Text("Windows");
            ImGui::Separator();

            if (ImGui::Checkbox("Show Debug Window", &config.display.show_debug)) {
                g_config.AutoSave();
            }
            if (ImGui::Checkbox("Show BassMIDI Status", &config.display.show_bassmidi_status)) {
                g_config.AutoSave();
            }
            if (ImGui::Checkbox("Show MIDI Input", &config.display.show_midi_input)) {
                g_config.AutoSave();
            }
            if (ImGui::Checkbox("Show Audio Limiter", &config.display.show_audio_limiter)) {
                g_config.AutoSave();
            }

            if (ImGui::Button("Reset All Keys")) {
                for (int i = 0; i < 128; ++i) {
                    piano.SetKeyPressed(i, false);
                }
            }

            // Quick Audio Panic Section
            ImGui::Spacing();
            ImGui::Separator();
            ImGui::Text("Quick Audio Controls");

            if (g_audio.IsInitialized()) {
                // Audio status indicator
                bool audio_working = g_audio.IsAudioWorking();
                if (audio_working) {
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Audio: OK");
                } else {
                    ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "Audio: ERROR");
                }

                ImGui::SameLine();
                if (ImGui::Button("Stop All Notes")) {
                    g_audio.EmergencyStopAllNotes();
                }

                ImGui::SameLine();
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.8f, 0.2f, 0.2f, 1.0f));
                if (ImGui::Button("PANIC")) {
                    g_audio.PanicRestart();
                }
                ImGui::PopStyleColor();
                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("Emergency restart audio engine");
                }
            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), "Audio: Not Initialized");
                if (ImGui::Button("Initialize Audio")) {
                    g_audio.Initialize();
                }
            }

            ImGui::End();
        }

        // Piano keyboard is now rendered directly with OpenGL (see below)

        // Debug window
        if (config.display.show_debug) {
            ImGui::Begin("Debug Info", &config.display.show_debug);
            ImGui::Text("Application average %.3f ms/frame (%.1f FPS)",
                       1000.0f / ImGui::GetIO().Framerate, ImGui::GetIO().Framerate);
            ImGui::Text("Pressed keys: %d", piano.GetPressedKeyCount());

            auto pressed_keys = piano.GetPressedKeys();
            if (!pressed_keys.empty()) {
                ImGui::Text("Active notes:");
                for (int note : pressed_keys) {
                    ImGui::SameLine();
                    ImGui::Text("%d", note);
                }
            }

            ImGui::Text("Mouse Position: (%.1f, %.1f)", ImGui::GetIO().MousePos.x, ImGui::GetIO().MousePos.y);
            ImGui::Text("Window Size: (%d, %d)", display_w, display_h);

            ImGui::Separator();
            ImGui::Text("Audio Engine: %s", g_audio.IsInitialized() ? "Initialized" : "Not Available");
            if (g_audio.IsInitialized()) {
                ImGui::Text("Audio Enabled: %s", piano.IsAudioEnabled() ? "Yes" : "No");
                ImGui::Text("Volume: %.2f", g_audio.GetVolume());
                ImGui::Text("Soundfont: %s", g_audio.IsSoundfontLoaded() ?
                    g_audio.GetCurrentSoundfontPath().c_str() : "None (using default sounds)");
                ImGui::Text("Polyphony: %d / %d", g_audio.GetCurrentPolyphony(), g_audio.GetMaxPolyphony());

                // Polyphony bar
                float polyphony_ratio = g_audio.GetMaxPolyphony() > 0 ?
                    (float)g_audio.GetCurrentPolyphony() / (float)g_audio.GetMaxPolyphony() : 0.0f;
                ImGui::ProgressBar(polyphony_ratio, ImVec2(-1, 0),
                    (std::to_string(g_audio.GetCurrentPolyphony()) + " / " + std::to_string(g_audio.GetMaxPolyphony())).c_str());

                // Polyphony setting
                ImGui::Spacing();
                ImGui::Text("Polyphony Settings:");

                // Ensure AudioEngine polyphony matches config (in case it was changed elsewhere)
                if (config.audio.polyphony != g_audio.GetMaxPolyphony()) {
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                }

                // Use a slider for common values (1-1000) and input field for higher values
                if (ImGui::SliderInt("Max Polyphony", &config.audio.polyphony, 1, 1000)) {
                    if (g_audio.SetMaxPolyphony(config.audio.polyphony)) {
                        std::cout << "Polyphony changed to: " << config.audio.polyphony << std::endl;
                        g_config.AutoSave();
                    }
                }

                // Input field for custom values up to 13,765,870
                ImGui::Text("Custom Value (1-13765870):");
                if (ImGui::InputInt("##polyphony_input", &config.audio.polyphony, 1, 1000)) {
                    // Clamp the value to valid range
                    if (config.audio.polyphony < 1) config.audio.polyphony = 1;
                    if (config.audio.polyphony > 13765870) config.audio.polyphony = 13765870;

                    if (g_audio.SetMaxPolyphony(config.audio.polyphony)) {
                        std::cout << "Polyphony changed to: " << config.audio.polyphony << std::endl;
                        g_config.AutoSave();
                    }
                }

                // Preset buttons for common polyphony values
                ImGui::Text("Presets:");
                if (ImGui::Button("16")) {
                    config.audio.polyphony = 16;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("32")) {
                    config.audio.polyphony = 32;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("64")) {
                    config.audio.polyphony = 64;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("128")) {
                    config.audio.polyphony = 128;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("256")) {
                    config.audio.polyphony = 256;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }

                // Second row of presets
                if (ImGui::Button("512")) {
                    config.audio.polyphony = 512;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("1000")) {
                    config.audio.polyphony = 1000;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("10000")) {
                    config.audio.polyphony = 10000;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }

                // Third row of presets
                if (ImGui::Button("50000")) {
                    config.audio.polyphony = 50000;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("500000")) {
                    config.audio.polyphony = 500000;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("1000000")) {
                    config.audio.polyphony = 1000000;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }

                // Fourth row of presets
                if (ImGui::Button("5000000")) {
                    config.audio.polyphony = 5000000;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("13765870 (Max)")) {
                    config.audio.polyphony = 13765870;
                    g_audio.SetMaxPolyphony(config.audio.polyphony);
                    g_config.AutoSave();
                }
            }

            ImGui::End();
        }

        // BassMIDI Status Window
        if (config.display.show_bassmidi_status) {
            ImGui::Begin("BassMIDI Status", &config.display.show_bassmidi_status);

            if (g_audio.IsInitialized()) {
                // Performance metrics
                ImGui::Text("Performance Metrics");
                ImGui::Separator();

                float cpu_usage = g_audio.GetCPUUsage();
                ImGui::Text("CPU Usage: %.2f%%", cpu_usage);
                ImGui::ProgressBar(cpu_usage / 100.0f, ImVec2(-1, 0),
                    (std::to_string(static_cast<int>(cpu_usage)) + "%").c_str());

                float render_time = g_audio.GetRenderingTime();
                ImGui::Text("Rendering Time: %.3f ms", render_time);

                // Voice and channel information
                ImGui::Spacing();
                ImGui::Text("Voice Information");
                ImGui::Separator();

                int current_voices = g_audio.GetCurrentPolyphony();
                int max_voices = g_audio.GetMaxPolyphony();
                ImGui::Text("Active Voices: %d / %d", current_voices, max_voices);

                float voice_ratio = max_voices > 0 ? (float)current_voices / (float)max_voices : 0.0f;
                ImGui::ProgressBar(voice_ratio, ImVec2(-1, 0),
                    (std::to_string(current_voices) + " / " + std::to_string(max_voices)).c_str());

                int active_channels = g_audio.GetActiveChannels();
                ImGui::Text("Active Channels: %d", active_channels);

                // Audio system information
                ImGui::Spacing();
                ImGui::Text("Audio System Information");
                ImGui::Separator();

                std::string audio_info = g_audio.GetAudioInfo();
                ImGui::TextWrapped("%s", audio_info.c_str());

                // Real-time statistics
                ImGui::Spacing();
                ImGui::Text("Real-time Statistics");
                ImGui::Separator();

                static float cpu_history[100] = {0};
                static int cpu_history_offset = 0;
                cpu_history[cpu_history_offset] = cpu_usage;
                cpu_history_offset = (cpu_history_offset + 1) % 100;

                ImGui::PlotLines("CPU Usage History", cpu_history, 100, cpu_history_offset,
                    "CPU %", 0.0f, 100.0f, ImVec2(0, 80));

                static float voice_history[100] = {0};
                static int voice_history_offset = 0;
                voice_history[voice_history_offset] = voice_ratio * 100.0f;
                voice_history_offset = (voice_history_offset + 1) % 100;

                ImGui::PlotLines("Voice Usage History", voice_history, 100, voice_history_offset,
                    "Voices %", 0.0f, 100.0f, ImVec2(0, 80));

                // Panic and Recovery Section
                ImGui::Spacing();
                ImGui::Text("Panic & Recovery");
                ImGui::Separator();

                // Audio status indicator
                bool audio_working = g_audio.IsAudioWorking();
                if (audio_working) {
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "● Audio Status: Working");
                } else {
                    ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "● Audio Status: Problem Detected!");
                }

                // Emergency Stop All Notes button
                if (ImGui::Button("Emergency Stop All Notes", ImVec2(-1, 0))) {
                    g_audio.EmergencyStopAllNotes();
                }
                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("Immediately stop all playing notes and reset MIDI channels");
                }

                // Panic Restart button
                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.8f, 0.2f, 0.2f, 1.0f));
                ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(1.0f, 0.3f, 0.3f, 1.0f));
                ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.6f, 0.1f, 0.1f, 1.0f));

                if (ImGui::Button("PANIC RESTART", ImVec2(-1, 0))) {
                    static bool confirm_panic = false;
                    if (!confirm_panic) {
                        confirm_panic = true;
                        ImGui::OpenPopup("Confirm Panic Restart");
                    }
                }
                ImGui::PopStyleColor(3);

                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("Emergency restart of the entire audio engine\n"
                                    "Use this when audio stops working completely\n"
                                    "WARNING: This will temporarily stop all audio!");
                }

                // Confirmation popup for panic restart
                if (ImGui::BeginPopupModal("Confirm Panic Restart", nullptr, ImGuiWindowFlags_AlwaysAutoResize)) {
                    ImGui::Text("Are you sure you want to restart the audio engine?");
                    ImGui::Text("This will temporarily stop all audio and may take a few seconds.");
                    ImGui::Separator();

                    if (ImGui::Button("Yes, Restart Audio Engine", ImVec2(200, 0))) {
                        if (g_audio.PanicRestart()) {
                            ImGui::CloseCurrentPopup();
                        }
                    }
                    ImGui::SameLine();
                    if (ImGui::Button("Cancel", ImVec2(100, 0))) {
                        ImGui::CloseCurrentPopup();
                    }
                    ImGui::EndPopup();
                }

                // Test tone button
                if (ImGui::Button("Test Audio", ImVec2(-1, 0))) {
                    g_audio.PlayTestTone();
                }
                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("Play a test tone (Middle C) to check if audio is working");
                }

            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Audio engine not initialized");
                ImGui::Text("BassMIDI status information is not available");

                // Show panic restart option even when not initialized
                ImGui::Spacing();
                ImGui::Text("Recovery Options");
                ImGui::Separator();

                if (ImGui::Button("Try Initialize Audio Engine", ImVec2(-1, 0))) {
                    if (g_audio.Initialize()) {
                        std::cout << "Audio engine initialized successfully!" << std::endl;
                    } else {
                        std::cout << "Failed to initialize audio engine" << std::endl;
                    }
                }
            }

            ImGui::End();
        }

        // MIDI Input Window
        if (config.display.show_midi_input) {
            ImGui::Begin("MIDI Input", &config.display.show_midi_input);

            if (g_audio.IsInitialized()) {
                // MIDI Input Type Selection
                ImGui::Text("MIDI Input Type");
                ImGui::Separator();

#ifdef _WIN32
                // Windows: Use Windows MIDI (winmm) or BASS MIDI
                if (ImGui::RadioButton("BASS MIDI Input", !config.midi.use_alsa_midi)) {
                    if (config.midi.use_alsa_midi) {
                        // Switch from Windows MIDI to BASS
                        g_audio.CloseWinMMMIDIDevice();
                        config.midi.selected_alsa_midi_device = -1;
                        config.midi.use_alsa_midi = false;
                        g_config.AutoSave();
                    }
                }
                ImGui::SameLine();
                if (ImGui::RadioButton("Windows MIDI Input", config.midi.use_alsa_midi)) {
                    if (!config.midi.use_alsa_midi) {
                        // Switch from BASS to Windows MIDI
                        g_audio.CloseMIDIInputDevice();
                        config.midi.selected_midi_device = -1;
                        config.midi.use_alsa_midi = true;
                        g_config.AutoSave();
                    }
                }
#else
                // Linux: Use ALSA MIDI or BASS MIDI
                if (ImGui::RadioButton("BASS MIDI Input", !config.midi.use_alsa_midi)) {
                    if (config.midi.use_alsa_midi) {
                        // Switch from ALSA to BASS
                        g_audio.CloseALSAMIDIDevice();
                        config.midi.selected_alsa_midi_device = -1;
                        config.midi.use_alsa_midi = false;
                        g_config.AutoSave();
                    }
                }
                ImGui::SameLine();
                if (ImGui::RadioButton("ALSA MIDI Input", config.midi.use_alsa_midi)) {
                    if (!config.midi.use_alsa_midi) {
                        // Switch from BASS to ALSA
                        g_audio.CloseMIDIInputDevice();
                        config.midi.selected_midi_device = -1;
                        config.midi.use_alsa_midi = true;
                        g_config.AutoSave();
                    }
                }
#endif

                ImGui::Spacing();
                ImGui::Separator();

                if (!config.midi.use_alsa_midi) {
                    // BASS MIDI Input Device Selection
                    ImGui::Text("BASS MIDI Input Devices");
                    ImGui::Separator();

                    std::vector<std::string> midi_devices = g_audio.GetMIDIInputDevices();

                    if (midi_devices.empty()) {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "No BASS MIDI input devices found");
                        ImGui::Text("Make sure MIDI devices are connected and drivers are installed");
                    } else {
                        ImGui::Text("Available BASS MIDI Input Devices:");

                        for (size_t i = 0; i < midi_devices.size(); i++) {
                            bool is_selected = (config.midi.selected_midi_device == static_cast<int>(i));

                            // Create unique ID for each selectable item
                            std::string device_label = midi_devices[i] + "##bass_midi_device_" + std::to_string(i);

                            if (ImGui::Selectable(device_label.c_str(), is_selected)) {
                                if (config.midi.selected_midi_device != static_cast<int>(i)) {
                                    config.midi.selected_midi_device = static_cast<int>(i);
                                    if (g_audio.OpenMIDIInputDevice(config.midi.selected_midi_device)) {
                                        std::cout << "Opened BASS MIDI input device: " << midi_devices[i] << std::endl;
                                        g_config.AutoSave();
                                    } else {
                                        std::cerr << "Failed to open BASS MIDI input device: " << midi_devices[i] << std::endl;
                                        config.midi.selected_midi_device = -1;
                                    }
                                }
                            }

                            if (is_selected) {
                                ImGui::SetItemDefaultFocus();
                            }
                        }
                    }
                } else {
#ifdef _WIN32
                    // Windows MIDI Input Device Selection
                    ImGui::Text("Windows MIDI Input Devices");
                    ImGui::Separator();

                    std::vector<WinMMMIDIDevice> winmm_devices = g_audio.GetWinMMMIDIDevices();

                    if (winmm_devices.empty()) {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "No Windows MIDI input devices found");
                        ImGui::Text("Make sure MIDI devices are connected and drivers are installed");
                    } else {
                        ImGui::Text("Available Windows MIDI Input Devices:");

                        for (size_t i = 0; i < winmm_devices.size(); i++) {
                            bool is_selected = (config.midi.selected_alsa_midi_device == static_cast<int>(i));

                            // Create unique ID for each selectable item
                            std::string device_label = winmm_devices[i].name + "##winmm_midi_device_" + std::to_string(i);

                            if (ImGui::Selectable(device_label.c_str(), is_selected)) {
                                if (config.midi.selected_alsa_midi_device != static_cast<int>(i)) {
                                    config.midi.selected_alsa_midi_device = static_cast<int>(i);
                                    if (g_audio.OpenWinMMMIDIDevice(winmm_devices[i].device_id)) {
                                        std::cout << "Opened Windows MIDI input device: " << winmm_devices[i].name << std::endl;
                                        g_config.AutoSave();
                                    } else {
                                        std::cerr << "Failed to open Windows MIDI input device: " << winmm_devices[i].name << std::endl;
                                        config.midi.selected_alsa_midi_device = -1;
                                    }
                                }
                            }

                            if (is_selected) {
                                ImGui::SetItemDefaultFocus();
                            }
                        }
                    }
#else
                    // ALSA MIDI Input Device Selection
                    ImGui::Text("ALSA MIDI Input Devices");
                    ImGui::Separator();

                    std::vector<ALSAMIDIDevice> alsa_devices = g_audio.GetALSAMIDIDevices();

                    if (alsa_devices.empty()) {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "No ALSA MIDI input devices found");
                        ImGui::Text("Make sure MIDI devices are connected and ALSA is properly configured");
                    } else {
                        ImGui::Text("Available ALSA MIDI Input Devices:");

                        for (size_t i = 0; i < alsa_devices.size(); i++) {
                            bool is_selected = (config.midi.selected_alsa_midi_device == static_cast<int>(i));

                            // Create unique ID for each selectable item
                            std::string device_label = alsa_devices[i].full_name + "##alsa_midi_device_" + std::to_string(i);

                            if (ImGui::Selectable(device_label.c_str(), is_selected)) {
                                if (config.midi.selected_alsa_midi_device != static_cast<int>(i)) {
                                    config.midi.selected_alsa_midi_device = static_cast<int>(i);
                                    if (g_audio.OpenALSAMIDIDevice(alsa_devices[i].client_id, alsa_devices[i].port_id)) {
                                        std::cout << "Opened ALSA MIDI input device: " << alsa_devices[i].full_name << std::endl;
                                        g_config.AutoSave();
                                    } else {
                                        std::cerr << "Failed to open ALSA MIDI input device: " << alsa_devices[i].full_name << std::endl;
                                        config.midi.selected_alsa_midi_device = -1;
                                    }
                                }
                            }

                            if (is_selected) {
                                ImGui::SetItemDefaultFocus();
                            }
                        }
                    }
#endif
                }

                ImGui::Spacing();
                ImGui::Separator();

                // Current MIDI Input Status
                ImGui::Text("MIDI Input Status");
                ImGui::Separator();

                if (!config.midi.use_alsa_midi) {
                    // BASS MIDI Status
                    if (g_audio.IsMIDIInputOpen()) {
                        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "BASS MIDI Input: ACTIVE");
                        ImGui::Text("Device: %s", g_audio.GetCurrentMIDIInputDevice().c_str());

                        if (ImGui::Button("Close BASS MIDI Input")) {
                            g_audio.CloseMIDIInputDevice();
                            config.midi.selected_midi_device = -1;
                        }
                    } else {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "BASS MIDI Input: INACTIVE");
                        ImGui::Text("No BASS MIDI input device is currently open");
                    }
                } else {
#ifdef _WIN32
                    // Windows MIDI Status
                    if (g_audio.IsWinMMMIDIOpen()) {
                        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Windows MIDI Input: ACTIVE");
                        WinMMMIDIDevice current_device = g_audio.GetCurrentWinMMMIDIDevice();
                        ImGui::Text("Device: %s", current_device.name.c_str());

                        if (ImGui::Button("Close Windows MIDI Input")) {
                            g_audio.CloseWinMMMIDIDevice();
                            config.midi.selected_alsa_midi_device = -1;
                        }
                    } else {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Windows MIDI Input: INACTIVE");
                        ImGui::Text("No Windows MIDI input device is currently open");
                    }
#else
                    // ALSA MIDI Status
                    if (g_audio.IsALSAMIDIOpen()) {
                        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "ALSA MIDI Input: ACTIVE");
                        ALSAMIDIDevice current_device = g_audio.GetCurrentALSAMIDIDevice();
                        ImGui::Text("Device: %s", current_device.full_name.c_str());

                        if (ImGui::Button("Close ALSA MIDI Input")) {
                            g_audio.CloseALSAMIDIDevice();
                            config.midi.selected_alsa_midi_device = -1;
                        }
                    } else {
                        ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "ALSA MIDI Input: INACTIVE");
                        ImGui::Text("No ALSA MIDI input device is currently open");
                    }
#endif
                }

                ImGui::Spacing();
                ImGui::Separator();

                // MIDI Input Instructions
                ImGui::Text("Instructions");
                ImGui::Separator();
                ImGui::TextWrapped("1. Connect your MIDI keyboard or controller");
#ifdef _WIN32
                ImGui::TextWrapped("2. Choose between BASS MIDI Input (cross-platform) or Windows MIDI Input (native Windows)");
#else
                ImGui::TextWrapped("2. Choose between BASS MIDI Input (cross-platform) or ALSA MIDI Input (native Linux)");
#endif
                ImGui::TextWrapped("3. Select a MIDI input device from the list above");
                ImGui::TextWrapped("4. Play notes on your MIDI device to hear them through the piano");
                ImGui::TextWrapped("5. MIDI Note On/Off messages will be automatically converted to piano notes");
                ImGui::Spacing();
#ifdef _WIN32
                ImGui::TextWrapped("Note: Windows MIDI Input provides better Windows compatibility and lower latency for native Windows MIDI devices.");
#else
                ImGui::TextWrapped("Note: ALSA MIDI Input provides better Linux compatibility and lower latency for native ALSA devices.");
#endif

            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Audio engine not initialized");
                ImGui::Text("MIDI input requires the audio engine to be running");
            }

            ImGui::End();
        }

        // Audio Limiter Debug Window
        if (config.display.show_audio_limiter) {
            ImGui::Begin("Audio Limiter", &config.display.show_audio_limiter);

            if (g_audio.IsInitialized()) {
                AudioLimiter* limiter = g_audio.GetAudioLimiter();

                // Enable/Disable Limiter
                ImGui::Text("Audio Limiter Control");
                ImGui::Separator();

                if (ImGui::Checkbox("Enable Audio Limiter", &config.audio.limiter_enabled)) {
                    g_audio.SetLimiterEnabled(config.audio.limiter_enabled);
                    g_config.AutoSave();
                }

                if (config.audio.limiter_enabled) {
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Status: ACTIVE");
                } else {
                    ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Status: DISABLED");
                }

                ImGui::Spacing();
                ImGui::Separator();

                // Limiter Parameters
                ImGui::Text("Limiter Parameters");
                ImGui::Separator();

                // Threshold
                if (ImGui::SliderFloat("Threshold (dB)", &config.audio.limiter_threshold, -60.0f, 0.0f, "%.1f dB")) {
                    limiter->SetThreshold(config.audio.limiter_threshold);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("Reset##threshold")) {
                    config.audio.limiter_threshold = -6.0f;
                    limiter->SetThreshold(config.audio.limiter_threshold);
                    g_config.AutoSave();
                }

                // Release Time
                if (ImGui::SliderFloat("Release (ms)", &config.audio.limiter_release_time, 5.0f, 200.0f, "%.0f ms")) {
                    limiter->SetReleaseTime(config.audio.limiter_release_time);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("Reset##release")) {
                    config.audio.limiter_release_time = 100.0f;
                    limiter->SetReleaseTime(config.audio.limiter_release_time);
                    g_config.AutoSave();
                }

                // Look-ahead Time
                if (ImGui::SliderFloat("Look-ahead (ms)", &config.audio.limiter_lookahead_time, 5.0f, 20.0f, "%.1f ms")) {
                    limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("Reset##lookahead")) {
                    config.audio.limiter_lookahead_time = 5.0f;
                    limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
                    g_config.AutoSave();
                }

                ImGui::Spacing();
                ImGui::Separator();

                // Real-time Monitoring
                ImGui::Text("Real-time Monitoring");
                ImGui::Separator();

                // Static variables for graph history
                static std::vector<float> gain_reduction_history(200, 0.0f);
                static std::vector<float> input_level_history(200, -60.0f);
                static std::vector<float> output_level_history(200, -60.0f);
                static int history_offset = 0;

                // Update history data
                float gain_reduction = limiter->GetGainReduction();
                float peak_level = limiter->GetPeakLevel();
                float output_level = limiter->GetOutputLevel();

                gain_reduction_history[history_offset] = gain_reduction;
                input_level_history[history_offset] = peak_level;
                output_level_history[history_offset] = output_level;
                history_offset = (history_offset + 1) % gain_reduction_history.size();

                // Gain Reduction Meter
                ImGui::Text("Gain Reduction: %.1f dB", gain_reduction);
                float gr_normalized = std::max(0.0f, -gain_reduction / 20.0f); // Normalize to 0-1
                ImGui::PushStyleColor(ImGuiCol_PlotHistogram, ImVec4(1.0f, 0.5f, 0.0f, 1.0f));
                ImGui::ProgressBar(gr_normalized, ImVec2(-1, 0),
                    (std::to_string(static_cast<int>(gain_reduction)) + " dB").c_str());
                ImGui::PopStyleColor();

                // Peak Level Meter
                ImGui::Text("Input Peak: %.1f dB", peak_level);
                float peak_normalized = std::max(0.0f, (peak_level + 60.0f) / 60.0f); // -60dB to 0dB
                ImVec4 peak_color = peak_level > -3.0f ? ImVec4(1.0f, 0.0f, 0.0f, 1.0f) :
                                   peak_level > -12.0f ? ImVec4(1.0f, 1.0f, 0.0f, 1.0f) :
                                   ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
                ImGui::PushStyleColor(ImGuiCol_PlotHistogram, peak_color);
                ImGui::ProgressBar(peak_normalized, ImVec2(-1, 0),
                    (std::to_string(static_cast<int>(peak_level)) + " dB").c_str());
                ImGui::PopStyleColor();

                // Output Level Meter
                ImGui::Text("Output Level: %.1f dB", output_level);
                float output_normalized = std::max(0.0f, (output_level + 60.0f) / 60.0f); // -60dB to 0dB
                ImVec4 output_color = output_level > -3.0f ? ImVec4(1.0f, 0.0f, 0.0f, 1.0f) :
                                     output_level > -12.0f ? ImVec4(1.0f, 1.0f, 0.0f, 1.0f) :
                                     ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
                ImGui::PushStyleColor(ImGuiCol_PlotHistogram, output_color);
                ImGui::ProgressBar(output_normalized, ImVec2(-1, 0),
                    (std::to_string(static_cast<int>(output_level)) + " dB").c_str());
                ImGui::PopStyleColor();

                ImGui::Spacing();
                ImGui::Separator();

                // Time-series Graphs
                ImGui::Text("Time-series Graphs");
                ImGui::Separator();

                // Gain Reduction Graph
                ImGui::Text("Gain Reduction History");
                ImGui::PlotLines("##GainReduction", gain_reduction_history.data(),
                    static_cast<int>(gain_reduction_history.size()), history_offset,
                    "Gain Reduction (dB)", -20.0f, 0.0f, ImVec2(0, 80));

                // Input Level Graph
                ImGui::Text("Input Level History");
                ImGui::PlotLines("##InputLevel", input_level_history.data(),
                    static_cast<int>(input_level_history.size()), history_offset,
                    "Input Level (dB)", -60.0f, 0.0f, ImVec2(0, 80));

                // Output Level Graph
                ImGui::Text("Output Level History");
                ImGui::PlotLines("##OutputLevel", output_level_history.data(),
                    static_cast<int>(output_level_history.size()), history_offset,
                    "Output Level (dB)", -60.0f, 0.0f, ImVec2(0, 80));

                ImGui::Spacing();
                ImGui::Separator();

                // Preset Buttons
                ImGui::Text("Presets");
                ImGui::Separator();

                if (ImGui::Button("Gentle Limiter")) {
                    config.audio.limiter_threshold = -12.0f;
                    config.audio.limiter_release_time = 200.0f;
                    config.audio.limiter_lookahead_time = 10.0f;
                    limiter->SetThreshold(config.audio.limiter_threshold);
                    limiter->SetReleaseTime(config.audio.limiter_release_time);
                    limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("Hard Limiter")) {
                    config.audio.limiter_threshold = -3.0f;
                    config.audio.limiter_release_time = 50.0f;
                    config.audio.limiter_lookahead_time = 5.0f;
                    limiter->SetThreshold(config.audio.limiter_threshold);
                    limiter->SetReleaseTime(config.audio.limiter_release_time);
                    limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
                    g_config.AutoSave();
                }
                ImGui::SameLine();
                if (ImGui::Button("Brick Wall")) {
                    config.audio.limiter_threshold = -1.0f;
                    config.audio.limiter_release_time = 10.0f;
                    config.audio.limiter_lookahead_time = 5.0f;
                    limiter->SetThreshold(config.audio.limiter_threshold);
                    limiter->SetReleaseTime(config.audio.limiter_release_time);
                    limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
                    g_config.AutoSave();
                }

                if (ImGui::Button("Transparent")) {
                    config.audio.limiter_threshold = -6.0f;
                    config.audio.limiter_release_time = 100.0f;
                    config.audio.limiter_lookahead_time = 8.0f;
                    limiter->SetThreshold(config.audio.limiter_threshold);
                    limiter->SetReleaseTime(config.audio.limiter_release_time);
                    limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
                    g_config.AutoSave();
                }

                if (ImGui::Button("Reset All")) {
                    config.audio.limiter_threshold = -6.0f;
                    config.audio.limiter_release_time = 100.0f;
                    config.audio.limiter_lookahead_time = 5.0f;
                    limiter->Reset();
                    limiter->SetThreshold(config.audio.limiter_threshold);
                    limiter->SetReleaseTime(config.audio.limiter_release_time);
                    limiter->SetLookAheadTime(config.audio.limiter_lookahead_time);
                    g_config.AutoSave();
                }

            } else {
                ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.5f, 1.0f), "Audio engine not initialized");
                ImGui::Text("Audio Limiter requires the audio engine to be running");
            }

            ImGui::End();
        }

        // Display file browser and handle file selection
        g_soundfont_browser.Display();
        if (g_soundfont_browser.HasSelected()) {
            auto selected_path = g_soundfont_browser.GetSelected();
            config.audio.soundfont_path = selected_path.string();
            g_config.AutoSave();

            // Update the soundfont buffer for display
            // This will be reflected in the input text field
            std::cout << "Selected soundfont file: " << config.audio.soundfont_path << std::endl;

            g_soundfont_browser.ClearSelected();
        }

        // Rendering
        ImGui::Render();

        // Clear screen with background color from config
        Color bg_color(config.display.background_color[0], config.display.background_color[1], config.display.background_color[2], 1.0f);
        renderer.Clear(bg_color);

        // Render piano keyboard with OpenGL
        piano.Render(renderer);

        // Render ImGui on top
        ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());

        glfwSwapBuffers(window);
    }

    // Cleanup
    ImGui_ImplOpenGL3_Shutdown();
    ImGui_ImplGlfw_Shutdown();
    ImGui::DestroyContext();

    glfwDestroyWindow(window);
    glfwTerminate();

    return 0;
}
