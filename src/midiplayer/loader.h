#ifndef MIDIPLAYER_LOADER_H
#define MIDIPLAYER_LOADER_H

#include <stdio.h>
#include <stdlib.h>
#include "midi_data.h"

#ifdef __cplusplus
extern "C" {
#endif

// MIDIファイルロード進行状況を追跡するための構造体
typedef struct {
    char filename[256];        // ロード中のファイル名
    float progress;            // 進行状況 (0.0 - 1.0)
    int totalTracks;           // 合計トラック数
    int loadedTracks;          // ロード済みトラック数
    char statusMessage[256];   // 現在のステータスメッセージ
    bool isLoading;            // ロード中かどうか
    bool hasError;             // エラーが発生したかどうか
    int totalTimeMs;           // MIDIファイルの全体時間（ミリ秒）
} MidiLoadingStatus;

// グローバル変数としてロード状態を宣言（外部ファイルからアクセス可能）
extern MidiLoadingStatus gMidiLoadingStatus;

// MIDIファイル読み込み関数
void *loadMidiFile(void *context);

// プログラム初期化のための関数を宣言
void initializePlaybackState(void);

#ifdef __cplusplus
}
#endif

#endif
